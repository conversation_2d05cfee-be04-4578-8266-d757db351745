# How to Run the CRUD Application with Authentication

## 🚀 Running in IntelliJ IDEA (Recommended)

### Step 1: Prepare the Database
1. **Start MySQL** and ensure it's running on port 3306
2. **Create the database** if it doesn't exist:
   ```sql
   CREATE DATABASE spring_crud;
   ```
3. **Verify connection** with username `rails` and password `password`

### Step 2: Run the Application
1. **Open IntelliJ IDEA** and ensure the project is loaded
2. **Locate the main class**: `src/main/java/org/library/crud/CrudApplication.java`
3. **Right-click** on `CrudApplication.java`
4. **Select** "Run 'CrudApplication.main()'" or click the green play button
5. **Wait** for the application to start (you'll see Spring Boot logs)

### Step 3: Verify the Application is Running
Look for these log messages:
```
Started CrudApplication in X.XXX seconds
Tomcat started on port(s): 8080 (http)
```

## 🧪 Testing the Authentication System

### 1. Open Your Browser
Navigate to: `http://localhost:8080`

### 2. Test the Authentication Flow

#### A. Sign Up (Create New User)
1. Go to: `http://localhost:8080/auth/signup`
2. Fill in the form:
   - Username: `testuser`
   - Email: `<EMAIL>`
   - Password: `password123`
   - Confirm Password: `password123`
3. Click "Sign Up"
4. You should be redirected to the login page with a success message

#### B. Login
1. Go to: `http://localhost:8080/auth/login`
2. Enter your credentials:
   - Username: `testuser`
   - Password: `password123`
3. Click "Login"
4. You should be redirected to the dashboard

#### C. Access Protected Resources
1. After login, you should see the dashboard at: `http://localhost:8080/dashboard`
2. Try accessing the API: `http://localhost:8080/api/persons`
3. Without login, you should be redirected to the login page

### 3. Test API Endpoints

#### Using Browser (after login):
- `GET http://localhost:8080/api/persons` - View all persons

#### Using curl (after getting session cookie):
```bash
# First, login to get session cookie
curl -c cookies.txt -X POST http://localhost:8080/auth/login \
  -d "username=testuser&password=password123" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -L

# Then use the cookie for API calls
curl -b cookies.txt -X GET http://localhost:8080/api/persons

# Create a new person
curl -b cookies.txt -X POST http://localhost:8080/api/persons \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","age":30}'
```

## 🧪 Running Tests

### In IntelliJ IDEA:
1. **Right-click** on `src/test/java` folder
2. **Select** "Run All Tests"
3. **Watch** the test results in the test runner window

### Individual Test Classes:
- Right-click on any test class (e.g., `UserServiceTest.java`)
- Select "Run 'UserServiceTest'"

## 🔍 Troubleshooting

### Common Issues:

#### 1. Database Connection Error
**Error**: `Communications link failure`
**Solution**: 
- Ensure MySQL is running
- Check database credentials in `application.properties`
- Verify database `spring_crud` exists

#### 2. Port Already in Use
**Error**: `Port 8080 was already in use`
**Solution**: 
- Stop other applications using port 8080
- Or change port in `application.properties`: `server.port=8081`

#### 3. Liquibase Migration Errors
**Error**: Liquibase changelog errors
**Solution**: 
- Check database permissions
- Ensure database is empty for first run
- Check migration files in `src/main/resources/db/changelog/`

#### 4. Compilation Errors
**Error**: Cannot find symbol or import errors
**Solution**: 
- **Refresh** the project: Right-click project → "Reload Maven Project"
- **Clean and rebuild**: Build → "Clean" then Build → "Rebuild Project"

### Checking Logs:
Look for these in the IntelliJ console:
- ✅ `Liquibase: Update has been successful`
- ✅ `Started CrudApplication`
- ✅ `Tomcat started on port(s): 8080`

## 📊 Expected Behavior

### 1. **Successful Startup**:
```
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.5.4)

INFO  o.l.crud.CrudApplication - Starting CrudApplication
INFO  liquibase.database - Set default schema name to spring_crud
INFO  liquibase.changelog - Reading from spring_crud.DATABASECHANGELOG
INFO  liquibase.lockservice - Successfully acquired change log lock
INFO  liquibase.changelog - Creating database history table with name: spring_crud.DATABASECHANGELOG
INFO  liquibase.changelog - Reading from spring_crud.DATABASECHANGELOG
INFO  liquibase.command - Update has been successful.
INFO  o.s.b.w.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http)
INFO  o.l.crud.CrudApplication - Started CrudApplication in 4.567 seconds
```

### 2. **Database Tables Created**:
After startup, check your MySQL database. You should see:
- `person` table
- `users` table  
- `user_roles` table
- `DATABASECHANGELOG` table (Liquibase tracking)

### 3. **Web Pages Working**:
- ✅ Login page: Clean form with username/password fields
- ✅ Signup page: Registration form with validation
- ✅ Dashboard: Welcome page showing available API endpoints
- ✅ API protection: Redirects to login when not authenticated

## 🎯 Success Criteria

Your application is working correctly if:

1. ✅ **Application starts** without errors
2. ✅ **Database tables** are created by Liquibase
3. ✅ **You can sign up** a new user
4. ✅ **You can login** with created credentials
5. ✅ **Dashboard loads** after successful login
6. ✅ **API endpoints** are protected (redirect to login when not authenticated)
7. ✅ **API endpoints** work when authenticated
8. ✅ **Tests pass** when run in IntelliJ

---

**Ready to start?** Open IntelliJ IDEA and run `CrudApplication.main()`! 🚀
