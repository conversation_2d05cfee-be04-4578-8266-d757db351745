package org.library.crud.util;

import org.library.crud.model.Person;
import org.library.crud.model.User;

import java.util.Set;

/**
 * Utility class for building test data objects
 */
public class TestDataBuilder {

    public static User.UserBuilder defaultUser() {
        return User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("encodedPassword")
                .roles(Set.of(User.Role.USER))
                .enabled(true);
    }

    public static User.UserBuilder adminUser() {
        return User.builder()
                .username("adminuser")
                .email("<EMAIL>")
                .password("encodedPassword")
                .roles(Set.of(User.Role.USER, User.Role.ADMIN))
                .enabled(true);
    }

    public static Person.PersonBuilder defaultPerson() {
        return Person.builder()
                .name("<PERSON>")
                .age(30);
    }

    public static Person.PersonBuilder personWithId(Long id) {
        return defaultPerson().id(id);
    }
}
