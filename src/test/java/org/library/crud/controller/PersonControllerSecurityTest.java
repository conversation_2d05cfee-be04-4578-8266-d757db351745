package org.library.crud.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.library.crud.model.Person;
import org.library.crud.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(PersonController.class)
@ActiveProfiles("test")
@SuppressWarnings("deprecation")
class PersonControllerSecurityTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PersonService personService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void getAllPersons_WithoutAuthentication_ReturnsUnauthorized() throws Exception {
        mockMvc.perform(get("/api/persons"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void getAllPersons_WithAuthentication_ReturnsPersons() throws Exception {
        // Given
        Person person1 = new Person(1L, "John Doe", 30);
        Person person2 = new Person(2L, "Jane Smith", 25);
        when(personService.getAllPersons()).thenReturn(Arrays.asList(person1, person2));

        // When & Then
        mockMvc.perform(get("/api/persons"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].name").value("John Doe"))
                .andExpect(jsonPath("$[1].name").value("Jane Smith"));
    }

    @Test
    void getPersonById_WithoutAuthentication_ReturnsUnauthorized() throws Exception {
        mockMvc.perform(get("/api/persons/1"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void getPersonById_WithAuthentication_ReturnsPerson() throws Exception {
        // Given
        Person person = new Person(1L, "John Doe", 30);
        when(personService.getPersonById(1L)).thenReturn(Optional.of(person));

        // When & Then
        mockMvc.perform(get("/api/persons/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("John Doe"))
                .andExpect(jsonPath("$.age").value(30));
    }

    @Test
    void createPerson_WithoutAuthentication_ReturnsUnauthorized() throws Exception {
        Person person = new Person(null, "John Doe", 30);

        mockMvc.perform(post("/api/persons")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(person)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void createPerson_WithAuthentication_CreatesPerson() throws Exception {
        // Given
        Person inputPerson = new Person(null, "John Doe", 30);
        Person savedPerson = new Person(1L, "John Doe", 30);
        when(personService.savePerson(any(Person.class))).thenReturn(savedPerson);

        // When & Then
        mockMvc.perform(post("/api/persons")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputPerson)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("John Doe"));
    }

    @Test
    void updatePerson_WithoutAuthentication_ReturnsUnauthorized() throws Exception {
        Person person = new Person(1L, "Updated Name", 35);

        mockMvc.perform(put("/api/persons/1")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(person)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void updatePerson_WithAuthentication_UpdatesPerson() throws Exception {
        // Given
        Person updatedPerson = new Person(1L, "Updated Name", 35);
        when(personService.updatePerson(anyLong(), any(Person.class))).thenReturn(updatedPerson);

        // When & Then
        mockMvc.perform(put("/api/persons/1")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updatedPerson)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Updated Name"))
                .andExpect(jsonPath("$.age").value(35));
    }

    @Test
    void deletePerson_WithoutAuthentication_ReturnsUnauthorized() throws Exception {
        mockMvc.perform(delete("/api/persons/1")
                .with(csrf()))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void deletePerson_WithAuthentication_DeletesPerson() throws Exception {
        mockMvc.perform(delete("/api/persons/1")
                .with(csrf()))
                .andExpect(status().isOk());
    }
}