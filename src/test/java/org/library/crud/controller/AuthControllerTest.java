package org.library.crud.controller;

import org.junit.jupiter.api.Test;
import org.library.crud.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuthController.class)
@ActiveProfiles("test")
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Test
    void loginPage_ReturnsLoginView() throws Exception {
        mockMvc.perform(get("/auth/login"))
                .andExpect(status().isOk())
                .andExpect(view().name("login"));
    }

    @Test
    void loginPage_WithError_ShowsErrorMessage() throws Exception {
        mockMvc.perform(get("/auth/login").param("error", "true"))
                .andExpect(status().isOk())
                .andExpect(view().name("login"))
                .andExpect(model().attribute("error", "Invalid username or password"));
    }

    @Test
    void loginPage_WithLogout_ShowsLogoutMessage() throws Exception {
        mockMvc.perform(get("/auth/login").param("logout", "true"))
                .andExpect(status().isOk())
                .andExpect(view().name("login"))
                .andExpect(model().attribute("message", "You have been logged out successfully"));
    }

    @Test
    void signupPage_ReturnsSignupView() throws Exception {
        mockMvc.perform(get("/auth/signup"))
                .andExpect(status().isOk())
                .andExpect(view().name("signup"));
    }

    @Test
    void processSignup_ValidData_RedirectsToLogin() throws Exception {
        // Given
        when(userService.createUser(anyString(), anyString(), anyString()))
                .thenReturn(null); // We don't need the return value for this test

        // When & Then
        mockMvc.perform(post("/auth/signup")
                .with(csrf())
                .param("username", "testuser")
                .param("email", "<EMAIL>")
                .param("password", "password123")
                .param("confirmPassword", "password123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/auth/login"));

        verify(userService).createUser("testuser", "<EMAIL>", "password123");
    }

    @Test
    void processSignup_PasswordMismatch_RedirectsToSignupWithError() throws Exception {
        mockMvc.perform(post("/auth/signup")
                .with(csrf())
                .param("username", "testuser")
                .param("email", "<EMAIL>")
                .param("password", "password123")
                .param("confirmPassword", "differentpassword"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/auth/signup"));

        verify(userService, never()).createUser(anyString(), anyString(), anyString());
    }

    @Test
    void processSignup_UserServiceThrowsException_RedirectsToSignupWithError() throws Exception {
        // Given
        when(userService.createUser(anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Username already exists"));

        // When & Then
        mockMvc.perform(post("/auth/signup")
                .with(csrf())
                .param("username", "existinguser")
                .param("email", "<EMAIL>")
                .param("password", "password123")
                .param("confirmPassword", "password123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/auth/signup"));

        verify(userService).createUser("existinguser", "<EMAIL>", "password123");
    }
}