package org.library.crud.integration;

import org.junit.jupiter.api.Test;
import org.library.crud.config.TestConfig;
import org.library.crud.model.User;
import org.library.crud.repository.UserRepository;
import org.library.crud.util.TestDataBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestBuilders.formLogin;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.response.SecurityMockMvcResultMatchers.authenticated;
import static org.springframework.security.test.web.servlet.response.SecurityMockMvcResultMatchers.unauthenticated;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@AutoConfigureWebMvc
@Import(TestConfig.class)
@ActiveProfiles("test")
@Transactional
class AuthenticationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Test
    void loginWithValidCredentials_ShouldSucceed() throws Exception {
        // Given - Create a test user
        User testUser = TestDataBuilder.defaultUser()
                .username("testuser")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .build();
        userRepository.save(testUser);

        // When & Then
        mockMvc.perform(formLogin("/auth/login")
                .user("testuser")
                .password("password123"))
                .andExpect(authenticated().withUsername("testuser"))
                .andExpect(redirectedUrl("/dashboard"));
    }

    @Test
    void loginWithInvalidCredentials_ShouldFail() throws Exception {
        mockMvc.perform(formLogin("/auth/login")
                .user("nonexistent")
                .password("wrongpassword"))
                .andExpect(unauthenticated())
                .andExpect(redirectedUrl("/auth/login?error=true"));
    }

    @Test
    void accessProtectedResource_WithoutAuthentication_ShouldRedirectToLogin() throws Exception {
        mockMvc.perform(get("/api/persons"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrlPattern("**/auth/login"));
    }

    @Test
    void signupWithValidData_ShouldCreateUserAndRedirectToLogin() throws Exception {
        mockMvc.perform(post("/auth/signup")
                .with(csrf())
                .param("username", "newuser")
                .param("email", "<EMAIL>")
                .param("password", "password123")
                .param("confirmPassword", "password123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/auth/login"));

        // Verify user was created
        assertTrue(userRepository.existsByUsername("newuser"));
        assertTrue(userRepository.existsByEmail("<EMAIL>"));
    }

    @Test
    void signupWithExistingUsername_ShouldRedirectWithError() throws Exception {
        // Given - Create existing user
        User existingUser = TestDataBuilder.defaultUser()
                .username("existinguser")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password"))
                .build();
        userRepository.save(existingUser);

        // When & Then
        mockMvc.perform(post("/auth/signup")
                .with(csrf())
                .param("username", "existinguser")
                .param("email", "<EMAIL>")
                .param("password", "password123")
                .param("confirmPassword", "password123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/auth/signup"));
    }

    @Test
    void dashboard_WithAuthentication_ShouldShowDashboard() throws Exception {
        // Given - Create and login user
        User testUser = TestDataBuilder.defaultUser()
                .username("dashboarduser")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .build();
        userRepository.save(testUser);

        // When & Then
        mockMvc.perform(formLogin("/auth/login")
                .user("dashboarduser")
                .password("password123"))
                .andExpect(authenticated());

        mockMvc.perform(get("/dashboard"))
                .andExpect(status().isOk())
                .andExpect(view().name("dashboard"))
                .andExpect(model().attribute("username", "dashboarduser"));
    }
}