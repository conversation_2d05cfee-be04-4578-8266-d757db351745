package org.library.crud.base;

import org.library.crud.config.TestConfig;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

/**
 * Base class for WebMvcTest with common configuration
 */
@WebMvcTest
@Import(TestConfig.class)
@ActiveProfiles("test")
public abstract class BaseWebMvcTest {
    // Common test configuration
}
