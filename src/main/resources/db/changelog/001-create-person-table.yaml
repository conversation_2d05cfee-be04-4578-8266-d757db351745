databaseChangeLog:
  - changeSet:
      id: 001-create-person-table
      author: system
      comment: Create person table for existing functionality
      changes:
        - createTable:
            tableName: person
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: VARCHAR(255)
                  constraints:
                    nullable: true
              - column:
                  name: age
                  type: INT
                  constraints:
                    nullable: false
