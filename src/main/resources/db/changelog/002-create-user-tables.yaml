databaseChangeLog:
  - changeSet:
      id: 002-create-users-table
      author: system
      comment: Create users table for authentication
      changes:
        - createTable:
            tableName: users
            columns:
              - column:
                  name: id
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: username
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: email
                  type: VARCHAR(100)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: password
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: enabled
                  type: BOOLEAN
                  defaultValueBoolean: true
                  constraints:
                    nullable: false

  - changeSet:
      id: 002-create-user-roles-table
      author: system
      comment: Create user_roles table for user authorities
      changes:
        - createTable:
            tableName: user_roles
            columns:
              - column:
                  name: user_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: role
                  type: VARCHAR(20)
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseTableName: user_roles
            baseColumnNames: user_id
            constraintName: fk_user_roles_user_id
            referencedTableName: users
            referencedColumnNames: id
            onDelete: CASCADE
