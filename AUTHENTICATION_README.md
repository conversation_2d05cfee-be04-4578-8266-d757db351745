# Authentication System Implementation

This document describes the authentication system that has been added to the CRUD application.

## 🔐 Features Implemented

### 1. **User Management**
- User registration with username, email, and password
- Password encryption using BCrypt
- Role-based authorization (USER, ADMIN)
- User account status management (enabled/disabled)

### 2. **Authentication Flow**
- Form-based login and registration
- Session-based authentication
- Secure logout functionality
- Automatic redirection to login for protected resources

### 3. **Database Schema**
- Liquibase migrations for database versioning
- `users` table for user information
- `user_roles` table for role management
- `person` table migration (existing functionality)

### 4. **Security Configuration**
- Spring Security integration
- Method-level security with `@PreAuthorize`
- CSRF protection
- Session management

## 📁 Project Structure

```
src/main/java/org/library/crud/
├── config/
│   └── SecurityConfig.java              # Security configuration
├── controller/
│   ├── AuthController.java              # Login/signup endpoints
│   ├── DashboardController.java         # Dashboard after login
│   └── PersonController.java            # Protected API endpoints
├── model/
│   ├── User.java                        # User entity
│   └── Person.java                      # Person entity (existing)
├── repository/
│   ├── UserRepository.java              # User data access
│   └── PersonRepository.java            # Person data access (existing)
└── service/
    ├── UserService.java                 # User business logic
    ├── CustomUserDetailsService.java   # Spring Security integration
    └── PersonService.java               # Person business logic (existing)

src/main/resources/
├── db/changelog/
│   ├── db.changelog-master.yaml         # Master changelog
│   ├── 001-create-person-table.yaml    # Person table migration
│   └── 002-create-user-tables.yaml     # User tables migration
├── templates/
│   ├── login.html                       # Login form
│   ├── signup.html                      # Registration form
│   └── dashboard.html                   # User dashboard
└── application.properties               # Updated with Liquibase config

src/test/java/org/library/crud/
├── service/
│   ├── UserServiceTest.java             # Unit tests for UserService
│   └── CustomUserDetailsServiceTest.java # Unit tests for UserDetailsService
├── controller/
│   ├── AuthControllerTest.java          # Unit tests for AuthController
│   └── PersonControllerSecurityTest.java # Security tests for PersonController
├── integration/
│   └── AuthenticationIntegrationTest.java # Full authentication flow tests
└── CrudApplicationTests.java            # Application context test
```

## 🚀 How to Run

### Prerequisites
1. **MySQL Database**: Ensure MySQL is running with:
   - Database: `spring_crud`
   - Username: `rails`
   - Password: `password`

2. **Java 21**: The project uses Java 21

### Running the Application

#### Option 1: IntelliJ IDEA (Recommended)
1. Open the project in IntelliJ IDEA
2. Right-click on `CrudApplication.java`
3. Select "Run CrudApplication.main()"
4. The application will start on `http://localhost:8080`

#### Option 2: Command Line (if Maven is available)
```bash
./mvnw spring-boot:run
```

### Running Tests

#### In IntelliJ IDEA:
1. Right-click on `src/test/java`
2. Select "Run All Tests"

#### Command Line:
```bash
./mvnw test
```

## 🧪 Testing the Authentication System

### 1. **Manual Testing**

1. **Access the application**: Go to `http://localhost:8080`
2. **Sign up**: Navigate to `/auth/signup` and create a new account
3. **Login**: Use your credentials at `/auth/login`
4. **Dashboard**: After login, you'll be redirected to `/dashboard`
5. **API Access**: Try accessing `/api/persons` - should work when authenticated

### 2. **API Testing with curl**

First, login to get session cookie:
```bash
# Login and save cookies
curl -X POST http://localhost:8080/auth/login \
  -d "username=yourusername&password=yourpassword" \
  -c cookies.txt \
  -L

# Test API endpoints with authentication
curl -X GET http://localhost:8080/api/persons \
  -b cookies.txt

# Create a new person
curl -X POST http://localhost:8080/api/persons \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","age":30}' \
  -b cookies.txt
```

### 3. **Test Scenarios**

✅ **Positive Tests:**
- User registration with valid data
- Login with correct credentials
- Access to protected endpoints when authenticated
- Logout functionality

❌ **Negative Tests:**
- Registration with existing username/email
- Login with incorrect credentials
- Access to protected endpoints without authentication
- Password mismatch during registration

## 🔧 Configuration

### Database Configuration
```properties
# Database
spring.datasource.url=***************************************
spring.datasource.username=rails
spring.datasource.password=password

# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true

# Liquibase Configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.yaml
spring.liquibase.enabled=true
```

### Security Configuration
- Form-based authentication
- Session timeout: Default Spring Security settings
- Password encoding: BCrypt
- CSRF protection: Enabled
- Method security: Enabled with `@PreAuthorize`

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE NOT NULL
);
```

### User Roles Table
```sql
CREATE TABLE user_roles (
    user_id BIGINT NOT NULL,
    role VARCHAR(20) NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## 🛡️ Security Features

1. **Password Security**: BCrypt hashing with salt
2. **Session Management**: Secure session handling
3. **CSRF Protection**: Enabled for all forms
4. **Role-based Access**: Method-level security
5. **Input Validation**: Server-side validation
6. **SQL Injection Prevention**: JPA/Hibernate protection

## 🧪 Test Coverage

The test suite includes:

- **Unit Tests**: 
  - UserService (7 test methods)
  - CustomUserDetailsService (4 test methods)
  
- **Integration Tests**:
  - AuthController (6 test methods)
  - PersonController Security (8 test methods)
  - Full Authentication Flow (6 test methods)

- **Test Database**: H2 in-memory database for tests
- **Test Profiles**: Separate configuration for testing

## 🚨 Troubleshooting

### Common Issues:

1. **Database Connection**: Ensure MySQL is running and accessible
2. **Port Conflicts**: Default port is 8080, change if needed
3. **Liquibase Issues**: Check database permissions and schema
4. **Session Issues**: Clear browser cookies if login problems persist

### Logs to Check:
- Spring Security debug logs
- Liquibase migration logs
- Application startup logs

## 🔄 Next Steps

1. **Run the application** using IntelliJ IDEA
2. **Execute the test suite** to verify functionality
3. **Test the authentication flow** manually
4. **Customize as needed** for your specific requirements

---

**Note**: This authentication system provides a solid foundation for user management and can be extended with additional features like password reset, email verification, or OAuth integration as needed.
