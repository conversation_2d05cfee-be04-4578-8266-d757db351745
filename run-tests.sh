#!/bin/bash

echo "=== CRUD Application Authentication Test ==="
echo "This script will help you test the authentication system."
echo ""

echo "1. First, make sure your MySQL database is running:"
echo "   - Database: spring_crud"
echo "   - Username: rails"
echo "   - Password: password"
echo ""

echo "2. To run the application in IntelliJ IDEA:"
echo "   - Right-click on CrudApplication.java"
echo "   - Select 'Run CrudApplication.main()'"
echo "   - Or use the green play button in the IDE"
echo ""

echo "3. To run tests in IntelliJ IDEA:"
echo "   - Right-click on src/test/java"
echo "   - Select 'Run All Tests'"
echo "   - Or run individual test classes"
echo ""

echo "4. Once the application is running, test these URLs:"
echo "   - http://localhost:8080/auth/login (Login page)"
echo "   - http://localhost:8080/auth/signup (Signup page)"
echo "   - http://localhost:8080/api/persons (Should redirect to login)"
echo "   - http://localhost:8080/dashboard (After login)"
echo ""

echo "5. Test the authentication flow:"
echo "   a) Go to signup page and create a user"
echo "   b) Login with the created user"
echo "   c) Access the dashboard"
echo "   d) Try to access /api/persons endpoints"
echo ""

echo "6. API Testing with curl (after login):"
echo "   # Get all persons"
echo "   curl -X GET http://localhost:8080/api/persons --cookie-jar cookies.txt --cookie cookies.txt"
echo ""
echo "   # Create a person"
echo "   curl -X POST http://localhost:8080/api/persons \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"name\":\"John Doe\",\"age\":30}' \\"
echo "        --cookie-jar cookies.txt --cookie cookies.txt"
echo ""

echo "=== Files Created ==="
echo "Authentication Components:"
echo "- User model and repository"
echo "- UserService and CustomUserDetailsService"
echo "- SecurityConfig with form-based authentication"
echo "- AuthController for login/signup"
echo "- HTML templates for login, signup, and dashboard"
echo "- Liquibase migrations for database schema"
echo ""

echo "Test Components:"
echo "- UserServiceTest"
echo "- CustomUserDetailsServiceTest"
echo "- AuthControllerTest"
echo "- PersonControllerSecurityTest"
echo "- AuthenticationIntegrationTest"
echo ""

echo "=== Next Steps ==="
echo "1. Run the application in IntelliJ IDEA"
echo "2. Run the tests to verify everything works"
echo "3. Test the authentication flow manually"
echo "4. Test the API endpoints with authentication"
echo ""
